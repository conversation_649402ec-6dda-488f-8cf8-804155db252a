<?php
declare(strict_types=1);

namespace Comave\Sales\Plugin;

use Magento\Sales\Model\ResourceModel\Order\Grid\Collection;

class OrderGridSkuEanSearchPlugin
{
    /**
     * Intercept addFieldToFilter to enable SKU/EAN searching.
     *
     * @param Collection $collection
     * @param \Closure $proceed
     * @param string $field
     * @param mixed $condition
     * @return Collection
     */
    public function aroundAddFieldToFilter(
        Collection $collection,
        \Closure $proceed,
        $field,
        $condition = null
    ) {
        if ($field !== 'sku_ean') {
            return $proceed($field, $condition);
        }

        // On first call, add necessary joins and grouping
        if (!$collection->getFlag('sku_ean_capability_added')) {
            $collection->setFlag('sku_ean_capability_added', true);

            // Join sales_order_item for SKU
            $collection->getSelect()->joinLeft(
                ['soi' => $collection->getTable('sales_order_item')],
                'main_table.entity_id = soi.order_id',
                []
            );

            // Join catalog_product_entity for row_id or entity_id mapping
            $collection->getSelect()->joinLeft(
                ['cpe' => $collection->getTable('catalog_product_entity')],
                'soi.product_id = cpe.entity_id',
                []
            );

            // Join EAN attribute values if attribute exists
            $eanAttrId = $this->getEanAttributeId($collection);
            if ($eanAttrId) {
                $joinCondition = $this->hasRowIdColumn($collection)
                    ? 'cpe.row_id = ean_attr.row_id'
                    : 'cpe.entity_id = ean_attr.entity_id';
                $joinCondition .= ' AND ean_attr.attribute_id = ' . $eanAttrId . ' AND ean_attr.store_id = 0';

                $collection->getSelect()->joinLeft(
                    ['ean_attr' => $collection->getTable('catalog_product_entity_varchar')],
                    $joinCondition,
                    []
                );
            }

            // Prevent duplicate rows
            $collection->getSelect()->group('main_table.entity_id');
        }

        // Build the CONCAT_WS expression for SKU and optional EAN
        $expressionParts = ['IFNULL(soi.sku, "")'];
        $eanAttrId = $this->getEanAttributeId($collection);
        if ($eanAttrId) {
            $expressionParts[] = 'IFNULL(ean_attr.value, "")';
        }
        $expression = 'CONCAT_WS(" ", ' . implode(', ', $expressionParts) . ')';

        // Determine the LIKE pattern
        if (is_array($condition) && isset($condition['like'])) {
            $pattern = $condition['like'];
        } else {
            $pattern = '%' . trim((string)$condition, '%') . '%';
        }

        // Apply the custom WHERE clause
        $collection->getSelect()->where(
            "$expression LIKE ?",
            $pattern
        );

        return $collection;
    }

    /**
     * Get the attribute_id for the EAN attribute (attribute_code = 'ean').
     */
    private function getEanAttributeId(Collection $collection): ?int
    {
        try {
            $connection = $collection->getConnection();
            $select = $connection->select()
                ->from(['ea' => $collection->getTable('eav_attribute')], ['attribute_id'])
                ->where('ea.entity_type_id = ?', 4)
                ->where('ea.attribute_code = ?', 'ean');

            $attributeId = $connection->fetchOne($select);
            return $attributeId ? (int)$attributeId : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Check if catalog_product_entity table uses row_id (Magento EE with staging).
     */
    private function hasRowIdColumn(Collection $collection): bool
    {
        try {
            $columns = $collection->getConnection()->describeTable(
                $collection->getTable('catalog_product_entity')
            );
            return isset($columns['row_id']);
        } catch (\Exception $e) {
            return false;
        }
    }
}
