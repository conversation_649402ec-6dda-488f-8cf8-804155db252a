<?php
declare(strict_types=1);

namespace Comave\Sales\Ui\Component\Listing\Column;

use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Ui\Component\Listing\Columns\Column;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\OrderFactory;
use Magento\Framework\App\ResourceConnection;

class SkuEan extends Column
{
    /**
     * @var OrderRepositoryInterface
     */
    private readonly OrderRepositoryInterface $orderRepository;

    /**
     * @var OrderFactory
     */
    private readonly OrderFactory $orderFactory;

    /**
     * @var ResourceConnection
     */
    private readonly ResourceConnection $resourceConnection;

    /**
     * @param ContextInterface $context
     * @param UiComponentFactory $uiComponentFactory
     * @param OrderRepositoryInterface $orderRepository
     * @param OrderFactory $orderFactory
     * @param ResourceConnection $resourceConnection
     * @param array $components
     * @param array $data
     */
    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        OrderRepositoryInterface $orderRepository,
        OrderFactory $orderFactory,
        ResourceConnection $resourceConnection,
        array $components = [],
        array $data = []
    ) {
        $this->orderRepository = $orderRepository;
        $this->orderFactory = $orderFactory;
        $this->resourceConnection = $resourceConnection;
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare data source for SKU/EAN column.
     *
     * @param array $dataSource
     * @return array
     */
    public function prepareDataSource(array $dataSource)
    {
        if (isset($dataSource['data']['items'])) {
            $fieldName = $this->getData('name');
            foreach ($dataSource['data']['items'] as &$item) {
                $item[$fieldName] = $this->getSkuEanForOrder((int)$item['entity_id']);
            }
        }
        return $dataSource;
    }

    /**
     * Get SKU and EAN values for an order.
     *
     * @param int $orderId
     * @return string
     */
    private function getSkuEanForOrder(int $orderId): string
    {
        $connection = $this->resourceConnection->getConnection();

        // Get SKUs from order items
        $select = $connection->select()
            ->from(['soi' => $this->resourceConnection->getTableName('sales_order_item')], ['sku'])
            ->where('soi.order_id = ?', $orderId)
            ->where('soi.parent_item_id IS NULL'); // Only parent items, not child items

        $skus = $connection->fetchCol($select);

        if (empty($skus)) {
            return '';
        }

        // Get EAN values if EAN attribute exists
        $eanValues = $this->getEanValues($skus);

        // Combine SKUs and EANs
        $result = [];
        foreach ($skus as $sku) {
            $parts = [$sku];
            if (isset($eanValues[$sku]) && !empty($eanValues[$sku])) {
                $parts[] = $eanValues[$sku];
            }
            $result[] = implode(' ', $parts);
        }

        return implode(', ', $result);
    }

    /**
     * Get EAN values for given SKUs.
     *
     * @param array $skus
     * @return array
     */
    private function getEanValues(array $skus): array
    {
        if (empty($skus)) {
            return [];
        }

        $connection = $this->resourceConnection->getConnection();

        // First, get the EAN attribute ID
        $eanAttrId = $this->getEanAttributeId();
        if (!$eanAttrId) {
            return [];
        }

        try {
            // Check what columns are available in catalog_product_entity_varchar
            $varcharColumns = $connection->describeTable(
                $this->resourceConnection->getTableName('catalog_product_entity_varchar')
            );

            // Determine the correct link field for varchar table
            $varcharLinkField = 'entity_id'; // Default
            if (isset($varcharColumns['row_id'])) {
                $varcharLinkField = 'row_id';
            }

            // Check if we're using row_id (Enterprise Edition) or entity_id for main table
            $useRowId = $this->hasRowIdColumn();
            $mainLinkField = $useRowId ? 'row_id' : 'entity_id';

            // Get product IDs for the SKUs
            $select = $connection->select()
                ->from(['cpe' => $this->resourceConnection->getTableName('catalog_product_entity')], [$mainLinkField, 'sku'])
                ->where('cpe.sku IN (?)', $skus);

            $productData = $connection->fetchPairs($select);
            if (empty($productData)) {
                return [];
            }

            // Get EAN values using the correct link field
            $select = $connection->select()
                ->from(['cpev' => $this->resourceConnection->getTableName('catalog_product_entity_varchar')], [$varcharLinkField, 'value'])
                ->where('cpev.' . $varcharLinkField . ' IN (?)', array_keys($productData))
                ->where('cpev.attribute_id = ?', $eanAttrId)
                ->where('cpev.store_id = 0');

            $eanData = $connection->fetchPairs($select);

            // Map EAN values back to SKUs
            $result = [];
            foreach ($productData as $linkId => $sku) {
                if (isset($eanData[$linkId])) {
                    $result[$sku] = $eanData[$linkId];
                }
            }

            return $result;

        } catch (\Exception $e) {
            // If there's any error, return empty array to prevent breaking the grid
            return [];
        }
    }

    /**
     * Get the attribute_id for the EAN attribute.
     *
     * @return int|null
     */
    private function getEanAttributeId(): ?int
    {
        try {
            $connection = $this->resourceConnection->getConnection();
            $select = $connection->select()
                ->from(['ea' => $this->resourceConnection->getTableName('eav_attribute')], ['attribute_id'])
                ->where('ea.entity_type_id = ?', 4) // Product entity type
                ->where('ea.attribute_code = ?', 'ean');

            $attributeId = $connection->fetchOne($select);
            return $attributeId ? (int)$attributeId : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Check if catalog_product_entity table uses row_id (Magento EE with staging).
     *
     * @return bool
     */
    private function hasRowIdColumn(): bool
    {
        try {
            $connection = $this->resourceConnection->getConnection();
            $columns = $connection->describeTable(
                $this->resourceConnection->getTableName('catalog_product_entity')
            );
            return isset($columns['row_id']);
        } catch (\Exception $e) {
            return false;
        }
    }
}
