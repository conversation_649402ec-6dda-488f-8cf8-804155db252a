<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd"
         name="sales_order_grid">
    <columns name="sales_order_columns">
        <column name="sku_ean" class="Comave\Sales\Ui\Component\Listing\Column\SkuEan" sortOrder="1000">
            <settings>
                <filter>text</filter>
                <label translate="true">SKU/EAN</label>
            </settings>
        </column>
    </columns>
</listing>
