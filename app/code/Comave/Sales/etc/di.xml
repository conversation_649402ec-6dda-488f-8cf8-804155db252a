<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magento\Sales\Block\Order\History" type="Comave\Sales\Block\Order\History"/>
    <preference for="Comave\Sales\Api\PostProcessingMessageInterface" type="Comave\Sales\Model\PostProcessingMessage"/>
    <virtualType name="OrderPostProcessingLogger" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">OrderPostProcessingLogger</argument>
            <argument name="loggerPath" xsi:type="string">order_post_processing</argument>
        </arguments>
    </virtualType>
    <virtualType name="OrderProcessorTypeManager" type="Comave\Sales\Model\ProcessorTypeManager">
        <arguments>
            <argument name="processorTypes" xsi:type="array">
                <item name="reward_type_processor" xsi:type="string">Comave\Sales\Model\Processor\Type\Reward</item>
                <item name="seller_email_type_processor" xsi:type="string">
                    Comave\Sales\Model\Processor\Type\SellerEmail
                </item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Comave\Sales\Model\Queue\Consumer\PlaceOrderPostProcessing">
        <arguments>
            <argument xsi:type="object" name="processorTypeManager">OrderProcessorTypeManager</argument>
            <argument xsi:type="object" name="logger">OrderPostProcessingLogger</argument>
        </arguments>
    </type>
    <type name="Comave\Sales\Model\Processor\Type\Reward">
        <arguments>
            <argument xsi:type="object" name="logger">OrderPostProcessingLogger</argument>
        </arguments>
    </type>
    <type name="Comave\Sales\Model\Processor\Type\SellerEmail">
        <arguments>
            <argument xsi:type="object" name="logger">OrderPostProcessingLogger</argument>
        </arguments>
    </type>
    <virtualType name="RewardTypeFactory" type="Comave\Sales\Model\RewardTypeFactory">
        <arguments>
            <argument name="rewardTypes" xsi:type="array">
                <item name="reward_type_lix" xsi:type="string">Comave\Sales\Model\Reward\Type\Lix</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Comave\Sales\Service\Reward\Points\Calculator">
        <arguments>
            <argument xsi:type="object" name="rewardTypeFactory">RewardTypeFactory</argument>
        </arguments>
    </type>

    <type name="Magento\Sales\Block\Adminhtml\Order\View\Items">
        <plugin name="appendImage" type="Comave\Sales\Plugin\AppendProductImage"/>
    </type>

    <type name="Magento\Sales\Block\Adminhtml\Order\View\Items\Renderer\DefaultRenderer">
        <plugin name="appendImageRenderer" type="Comave\Sales\Plugin\AppendProductImageRenderer"/>
    </type>

    <type name="Magento\Quote\Model\Quote">
        <plugin name="preventMultipleSellerProducts" type="Comave\Sales\Plugin\PreventMultipleSellerItems"/>
    </type>
    <type name="Magento\Sales\Controller\Adminhtml\Order\AddComment">
        <plugin name="allowDeliveredStatus"
                type="Comave\Sales\Plugin\AllowDeliveredStatus"
                sortOrder="1"/>
    </type>

    <preference for="Comave\Sales\Api\Data\Order\NotificationInterface" type="Comave\Sales\Model\Order\Notification"/>
    <preference for="Comave\Sales\Api\Data\Order\NotificationSearchResultInterface"
                type="Comave\Sales\Model\Order\NotificationSearchResults"/>
    <preference for="Comave\Sales\Api\Order\NotificationRepositoryInterface"
                type="Comave\Sales\Model\Order\NotificationRepository"/>
    <preference for="Comave\Sales\Api\Order\NotificationListRepositoryInterface"
                type="Comave\Sales\Model\Order\NotificationListRepository"/>
    <type name="Magento\Framework\EntityManager\MetadataPool">
        <arguments>
            <argument name="metadata" xsi:type="array">
                <item name="Comave\Sales\Api\Data\Order\NotificationInterface" xsi:type="array">
                    <item name="entityTableName" xsi:type="string">sales_order_notification_history</item>
                    <item name="identifierField" xsi:type="string">notification_id</item>
                </item>
            </argument>
        </arguments>
    </type>
    <type name="Comave\Sales\Model\ResourceModel\Order\Notification">
        <arguments>
            <argument name="interfaceClass" xsi:type="string">Comave\Sales\Api\Data\Order\NotificationInterface
            </argument>
        </arguments>
    </type>
    <type name="Comave\Sales\Model\ResourceModel\Order\Notification\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">sales_order_notification_history</argument>
            <argument name="model" xsi:type="string">Comave\Sales\Model\Order\Notification</argument>
            <argument name="resourceModel" xsi:type="string">Comave\Sales\Model\ResourceModel\Order\Notification
            </argument>
            <argument name="idFieldName" xsi:type="string">notification_id</argument>
            <argument name="eventPrefix" xsi:type="string">sales_order_notification_history</argument>
            <argument name="eventObject" xsi:type="string">sales_order_notification_history</argument>
            <argument name="interfaceClass" xsi:type="string">Comave\Sales\Api\Data\Order\NotificationInterface
            </argument>
        </arguments>
    </type>

    <preference for="Comave\Sales\Api\Data\Order\TrackingInterface" type="Comave\Sales\Model\Order\Tracking"/>
    <preference for="Comave\Sales\Api\Data\Order\TrackingSearchResultInterface"
                type="Comave\Sales\Model\Order\TrackingSearchResults"/>
    <preference for="Comave\Sales\Api\Order\TrackingRepositoryInterface"
                type="Comave\Sales\Model\Order\TrackingRepository"/>
    <preference for="Comave\Sales\Api\Order\TrackingListRepositoryInterface"
                type="Comave\Sales\Model\Order\TrackingListRepository"/>
    <type name="Magento\Framework\EntityManager\MetadataPool">
        <arguments>
            <argument name="metadata" xsi:type="array">
                <item name="Comave\Sales\Api\Data\Order\TrackingInterface" xsi:type="array">
                    <item name="entityTableName" xsi:type="string">comave_seller_order_tracking</item>
                    <item name="identifierField" xsi:type="string">tracking_id</item>
                </item>
            </argument>
        </arguments>
    </type>
    <type name="Comave\Sales\Model\ResourceModel\Order\Tracking">
        <arguments>
            <argument name="interfaceClass" xsi:type="string">Comave\Sales\Api\Data\Order\TrackingInterface</argument>
        </arguments>
    </type>
    <type name="Comave\Sales\Model\ResourceModel\Order\Tracking\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">comave_seller_order_tracking</argument>
            <argument name="model" xsi:type="string">Comave\Sales\Model\Order\Tracking</argument>
            <argument name="resourceModel" xsi:type="string">Comave\Sales\Model\ResourceModel\Order\Tracking</argument>
            <argument name="idFieldName" xsi:type="string">tracking_id</argument>
            <argument name="eventPrefix" xsi:type="string">comave_seller_order_tracking</argument>
            <argument name="eventObject" xsi:type="string">comave_seller_order_tracking</argument>
            <argument name="interfaceClass" xsi:type="string">Comave\Sales\Api\Data\Order\TrackingInterface
            </argument>
        </arguments>
    </type>
    <type name="Comave\Sales\Service\Order\TrackingService">
        <arguments>
            <argument xsi:type="object" name="logger">OrderSyncLogger</argument>
        </arguments>
    </type>

    <type name="Magento\Sales\Model\ResourceModel\Order\Grid\Collection">
        <plugin name="comave_sales_order_grid_sku_ean_search" type="Comave\Sales\Plugin\OrderGridSkuEanSearchPlugin" />
    </type>

</config>